import { Task } from "../task/Task"
import { ClineSayTool } from "../../shared/ExtensionMessage"
import { formatResponse } from "../prompts/responses"
import { ToolUse, AskApproval, HandleError, PushToolResult, RemoveClosingTag } from "../../shared/tools"
import { UrlContentFetcher } from "../../services/url-content/UrlContentFetcher"

export async function fetchUrlContentTool(
	cline: Task,
	block: ToolUse,
	_askApproval: AskApproval,
	handleError: HandleError,
	pushToolResult: PushToolResult,
	_removeClosingTag: RemoveClosingTag,
) {
	console.log("[fetchUrlContentTool] Called with params:", JSON.stringify(block.params, null, 2))
	console.log("[fetchUrlContentTool] Block partial:", block.partial)
	console.log("[fetchUrlContentTool] Block type:", block.type)

	// Handle partial tool calls - wait for completion
	if (block.partial) {
		console.log("[fetchUrlContentTool] Partial call detected, waiting for completion...")
		return
	}

	const url: string | undefined = block.params.url

	if (!url) {
		console.log("[fetchUrlContentTool] No URL provided in params:", JSON.stringify(block.params, null, 2))
		pushToolResult(formatResponse.toolError("URL parameter is required. Use <url>https://example.com</url> format with the complete URL."))
		return
	}

	console.log("[fetchUrlContentTool] Processing URL:", url)
	console.log("[fetchUrlContentTool] URL type:", typeof url)
	console.log("[fetchUrlContentTool] URL length:", url.length)

	// Validate URL format
	const validation = await UrlContentFetcher.validateUrl(url)
	if (!validation.valid) {
		pushToolResult(formatResponse.toolError(`Invalid URL: ${validation.error}`))
		return
	}

	const sharedMessageProps: ClineSayTool = {
		tool: "fetchUrlContent",
		url: url
	}

	try {
		// Handle partial message first
		if (block.partial) {
			const partialMessage = JSON.stringify({
				...sharedMessageProps,
				content: undefined,
			} satisfies ClineSayTool)
			await cline.ask("tool", partialMessage, block.partial).catch(() => {})
			return
		}

		// Ask for approval to fetch the URL
		const completeMessage = JSON.stringify({
			...sharedMessageProps,
			content: undefined,
		} satisfies ClineSayTool)

		const { response, text, images } = await cline.ask("tool", completeMessage, false)

		if (response !== "yesButtonClicked") {
			// Handle rejection or feedback
			if (text) {
				await cline.say("user_feedback", text, images)
			}
			pushToolResult("URL content fetch was cancelled by the user.")
			return
		}

		// Fetch the URL content
		const result = await UrlContentFetcher.fetchUrlContent(url)

		if (!result.success) {
			const errorMessage = `Failed to fetch URL content: ${result.error}`
			pushToolResult(formatResponse.toolError(errorMessage))
			return
		}

		// Format the response with title and content
		let responseContent = ""

		if (result.title) {
			responseContent += `# ${result.title}\n\n`
		}

		responseContent += `**URL:** ${result.url}\n\n`
		responseContent += `**Content:**\n\n${result.content}`

		// Return the content to the AI
		pushToolResult(formatResponse.toolResult(responseContent))

	} catch (error) {
		await handleError("fetch URL content", error instanceof Error ? error : new Error(String(error)))
		pushToolResult(formatResponse.toolError(`Error fetching URL content: ${error instanceof Error ? error.message : String(error)}`))
	}
}
