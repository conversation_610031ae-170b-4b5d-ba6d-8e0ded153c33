import { ToolArgs } from "./types"

export function getFetchUrlContentDescription(args: ToolArgs): string {
	return `## fetch_url_content
Description: Request to fetch and read the contents of a webpage at a given URL. The tool extracts the main content from the webpage, converts it to clean markdown format, and returns it for analysis. This is useful for reading documentation, articles, blog posts, or any web content that needs to be analyzed or referenced.

The tool automatically:
- Extracts the main content using Mozilla's Readability algorithm (same as Firefox Reader Mode)
- Converts HTML to clean, readable markdown
- Removes navigation, ads, and other non-content elements
- Handles various webpage formats and structures
- Limits content to prevent overwhelming responses (truncates at 20,000 characters)

Parameters:
- url: (required) The complete URL to fetch content from (must include http:// or https://)

Usage:
<fetch_url_content>
<url>https://example.com/article</url>
</fetch_url_content>

IMPORTANT: Always provide the COMPLETE URL in a single call. Do not split the URL across multiple lines or calls.

Examples:

1. Fetching content from a documentation page:
<fetch_url_content>
<url>https://docs.example.com/api-reference</url>
</fetch_url_content>

2. Fetching content from a blog post:
<fetch_url_content>
<url>https://blog.example.com/how-to-guide</url>
</fetch_url_content>

3. Fetching content from a GitHub README:
<fetch_url_content>
<url>https://github.com/user/repo/blob/main/README.md</url>
</fetch_url_content>

Examples:

1. Reading a documentation page:
<fetch_url_content>
<url>https://docs.example.com/api-reference</url>
</fetch_url_content>

2. Fetching content from a blog post:
<fetch_url_content>
<url>https://blog.example.com/how-to-guide</url>
</fetch_url_content>

3. Reading a GitHub README:
<fetch_url_content>
<url>https://github.com/user/repo/blob/main/README.md</url>
</fetch_url_content>

IMPORTANT Notes:
- Only use this for web URLs (http/https), NOT for local files
- The tool works best with content-rich pages (articles, documentation, blogs)
- Content is automatically truncated if it exceeds 20,000 characters
- Some dynamic content or JavaScript-heavy sites may not be fully captured
- Always verify the URL is accessible and contains the content you need`
}
